%YAML:1.0

#--------------------------------------------------------------------------------------------
# System config
#--------------------------------------------------------------------------------------------

# When the variables are commented, the system doesn't load a previous session or not store the current one

# If the LoadFile doesn't exist, the system give a message and create a new Atlas from scratch
#System.LoadAtlasFromFile: "Session_MH01_MH02_MH03_Mono"

# The store file is created from the current session, if a file with the same name exists it is deleted
#System.SaveAtlasToFile: "Session_MH01_MH02_MH03_Mono"

#--------------------------------------------------------------------------------------------
# Camera Parameters. Adjust them!
#--------------------------------------------------------------------------------------------
File.version: "1.0"

Camera.type: "PinHole"

# Camera calibration and distortion parameters (OpenCV) 
Camera1.fx: 1236.996438
Camera1.fy: 1236.748984
Camera1.cx: 651.355418
Camera1.cy: 512.285855

Camera1.k1: -0.1419322617307899
Camera1.k2: 0.05865966713236987
Camera1.p1: 0.001520840995510708
Camera1.p2: 0.001582820035262499

Camera.width: 1280
Camera.height: 1024

Camera.newWidth: 640 # 代表缩放后的图像宽度
Camera.newHeight: 512 # 代表缩放后的图像高度

# Camera frames per second 
Camera.fps: 30

# Color order of the images (0: BGR, 1: RGB. It is ignored if images are grayscale)
Camera.RGB: 1

#--------------------------------------------------------------------------------------------
# ORB 参数
#--------------------------------------------------------------------------------------------

# ORB Extractor: 每张图像的特征数
ORBextractor.nFeatures: 1000


# ORB Extractor: 尺度金字塔的缩放因子
ORBextractor.scaleFactor: 1.2

# ORB Extractor: 尺度金字塔的层数
ORBextractor.nLevels: 8

# ORB Extractor: 快速阈值
# 图像被划分为网格。在每个单元格中提取FAST特征，并施加最小响应。
# 首先施加iniThFAST。如果没有检测到角点，则施加较低的值minThFAST
# 如果您的图像对比度较低，可以降低这些值
ORBextractor.iniThFAST: 20
ORBextractor.minThFAST: 7

#--------------------------------------------------------------------------------------------
# 可视化参数
#---------------------------------------------------------------------------------------------
Viewer.KeyFrameSize: 0.05
Viewer.KeyFrameLineWidth: 1.0
Viewer.GraphLineWidth: 0.9
Viewer.PointSize: 2.0
Viewer.CameraSize: 0.08
Viewer.CameraLineWidth: 3.0
Viewer.ViewpointX: 0.0
Viewer.ViewpointY: -0.7
Viewer.ViewpointZ: -1.8
Viewer.ViewpointF: 500.0

